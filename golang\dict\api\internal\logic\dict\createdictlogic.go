package dict

import (
	"context"
	"errors"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 创建字典
func NewCreateDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictLogic {
	return &CreateDictLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateDictLogic) CreateDict(req *types.CreateDictReq) (resp *types.CreateDictResp, err error) {
	// 参数验证
	if req.Code == "" {
		return nil, errors.New("字典编码不能为空")
	}
	if req.Name == "" {
		return nil, errors.New("字典名称不能为空")
	}

	// 设置默认状态
	if req.Status == 0 {
		req.Status = 1 // 默认启用
	}
	
	// 调用RPC服务创建字典
	// TODO: 这里需要添加RPC客户端调用
	// 由于当前没有配置RPC客户端，暂时返回模拟响应

	l.Logger.Infof("创建字典请求: 编码=%s, 名称=%s, 备注=%s, 状态=%d",
		req.Code, req.Name, req.Remark, req.Status)

	return &types.CreateDictResp{
		Id:      1, // 模拟ID，实际应该从RPC服务返回
		Message: "创建字典成功",
	}, nil
}
